
## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 11:11:44",
    "user_agent": "Mo<PERSON>\/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:D30"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.43,
    "memory_used_mb": 0.14,
    "peak_memory_mb": 0.49
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "R - Example Name",
    "lookup_name": "R - Example Name",
    "date": "2025-03-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 5,
    "lookup_name": "R - Example Name"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 5,
    "date": "2025-03-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "C - Example Name",
    "lookup_name": "Example Name",
    "date": "2025-03-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 6,
    "lookup_name": "Example Name"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 6,
    "date": "2025-03-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 16,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 23,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "C- DANNY ALCOCK",
    "lookup_name": "DANNY ALCOCK",
    "date": "2025-03-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 13,
    "lookup_name": "DANNY ALCOCK"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 13,
    "date": "2025-03-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "C - WILLIAM HUNT",
    "lookup_name": "WILLIAM HUNT",
    "date": "2025-03-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 8,
    "lookup_name": "WILLIAM HUNT"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 8,
    "date": "2025-03-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-03-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "C - JOE ROBSON ",
    "lookup_name": "JOE ROBSON",
    "date": "2025-04-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 14,
    "lookup_name": "JOE ROBSON"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 14,
    "date": "2025-04-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "C-David Andrew Jones",
    "lookup_name": "David Andrew Jones",
    "date": "2025-04-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 15,
    "lookup_name": "David Andrew Jones"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 15,
    "date": "2025-04-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "C - Luke Parker",
    "lookup_name": "Luke Parker",
    "date": "2025-04-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 16,
    "lookup_name": "Luke Parker"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 16,
    "date": "2025-04-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "C - Chamal Hettiarachchi",
    "lookup_name": "Chamal Hettiarachchi",
    "date": "2025-04-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 17,
    "lookup_name": "Chamal Hettiarachchi"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 17,
    "date": "2025-04-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 12,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-12"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "C - Oli Mason",
    "lookup_name": "Oli Mason",
    "date": "2025-04-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 18,
    "lookup_name": "Oli Mason"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 18,
    "date": "2025-04-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 23,
    "original_name": "Liv Patient - B. Hudges \/\/ Staff",
    "lookup_name": "Liv Patient - B. Hudges \/\/ Staff",
    "date": "2025-04-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 19,
    "lookup_name": "Liv Patient - B. Hudges \/\/ Staff"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 19,
    "date": "2025-04-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 24,
    "original_name": "C - Ben Arney",
    "lookup_name": "Ben Arney",
    "date": "2025-04-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 20,
    "lookup_name": "Ben Arney"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 20,
    "date": "2025-04-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "C - Damien Conybeare-Jones",
    "lookup_name": "Damien Conybeare-Jones",
    "date": "2025-04-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 21,
    "lookup_name": "Damien Conybeare-Jones"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 21,
    "date": "2025-04-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-04-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "C-Lucifer OShea",
    "lookup_name": "Lucifer OShea",
    "date": "2025-04-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 22,
    "lookup_name": "Lucifer OShea"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 22,
    "date": "2025-04-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "C - Priyesh Pattni",
    "lookup_name": "Priyesh Pattni",
    "date": "2025-04-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 23,
    "lookup_name": "Priyesh Pattni"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 23,
    "date": "2025-04-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 10,
    "recorded_count": 10,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "C - Daniel Ryan Hall",
    "lookup_name": "Daniel Ryan Hall",
    "date": "2025-05-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 24,
    "lookup_name": "Daniel Ryan Hall"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 24,
    "date": "2025-05-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "C - William Michael Pentland ",
    "lookup_name": "William Michael Pentland",
    "date": "2025-05-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 25,
    "lookup_name": "William Michael Pentland"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 25,
    "date": "2025-05-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "Closed \/\/ Surgeon is away",
    "lookup_name": "Closed \/\/ Surgeon is away",
    "date": "2025-05-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed \/\/ Surgeon is away"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "Closed \/\/ Surgeon is away",
    "lookup_name": "Closed \/\/ Surgeon is away",
    "date": "2025-05-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed \/\/ Surgeon is away"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "Closed \/\/ Surgeon is away",
    "lookup_name": "Closed \/\/ Surgeon is away",
    "date": "2025-05-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed \/\/ Surgeon is away"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "Closed \/\/ Surgeon is away",
    "lookup_name": "Closed \/\/ Surgeon is away",
    "date": "2025-05-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed \/\/ Surgeon is away"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 12,
    "original_name": "C - Steve Pardoe",
    "lookup_name": "Steve Pardoe",
    "date": "2025-05-12"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 26,
    "lookup_name": "Steve Pardoe"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 26,
    "date": "2025-05-12"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "C - Nate Turner",
    "lookup_name": "Nate Turner",
    "date": "2025-05-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 27,
    "lookup_name": "Nate Turner"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 27,
    "date": "2025-05-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "C - Andy Newell",
    "lookup_name": "Andy Newell",
    "date": "2025-05-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 28,
    "lookup_name": "Andy Newell"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 28,
    "date": "2025-05-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "C - Thomas Gray",
    "lookup_name": "Thomas Gray",
    "date": "2025-05-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 29,
    "lookup_name": "Thomas Gray"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 29,
    "date": "2025-05-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 16,
    "original_name": "C - Joe Callaway",
    "lookup_name": "Joe Callaway",
    "date": "2025-05-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 30,
    "lookup_name": "Joe Callaway"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 30,
    "date": "2025-05-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 17,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-17"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "c - Sipan Mohammed",
    "lookup_name": "c - Sipan Mohammed",
    "date": "2025-05-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 31,
    "lookup_name": "c - Sipan Mohammed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 31,
    "date": "2025-05-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "C - Bradley Wilson ",
    "lookup_name": "Bradley Wilson",
    "date": "2025-05-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 32,
    "lookup_name": "Bradley Wilson"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 32,
    "date": "2025-05-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "C - TONY MORGAN",
    "lookup_name": "TONY MORGAN",
    "date": "2025-05-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 33,
    "lookup_name": "TONY MORGAN"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 33,
    "date": "2025-05-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "C - Kris Bell",
    "lookup_name": "Kris Bell",
    "date": "2025-05-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 34,
    "lookup_name": "Kris Bell"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 34,
    "date": "2025-05-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 23,
    "original_name": "C - Jack Hanney ",
    "lookup_name": "Jack Hanney",
    "date": "2025-05-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 35,
    "lookup_name": "Jack Hanney"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 35,
    "date": "2025-05-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 24,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-05-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "C - John Paul Walsh",
    "lookup_name": "John Paul Walsh",
    "date": "2025-05-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 36,
    "lookup_name": "John Paul Walsh"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 36,
    "date": "2025-05-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "C - Andrew Lewis",
    "lookup_name": "Andrew Lewis",
    "date": "2025-05-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 37,
    "lookup_name": "Andrew Lewis"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 37,
    "date": "2025-05-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "C - Bob Cesna",
    "lookup_name": "Bob Cesna",
    "date": "2025-05-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 38,
    "lookup_name": "Bob Cesna"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 38,
    "date": "2025-05-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 15,
    "recorded_count": 15,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "C - Daniel Wilson Dunwell",
    "lookup_name": "Daniel Wilson Dunwell",
    "date": "2025-06-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 39,
    "lookup_name": "Daniel Wilson Dunwell"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 39,
    "date": "2025-06-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "C - Wojciech Etz",
    "lookup_name": "Wojciech Etz",
    "date": "2025-06-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 40,
    "lookup_name": "Wojciech Etz"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 40,
    "date": "2025-06-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "C - Dan Storey",
    "lookup_name": "Dan Storey",
    "date": "2025-06-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 41,
    "lookup_name": "Dan Storey"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 41,
    "date": "2025-06-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "C - Jamie Wanless",
    "lookup_name": "Jamie Wanless",
    "date": "2025-06-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 42,
    "lookup_name": "Jamie Wanless"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 42,
    "date": "2025-06-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 16,
    "original_name": "C - Sean Mullins ",
    "lookup_name": "Sean Mullins",
    "date": "2025-06-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 43,
    "lookup_name": "Sean Mullins"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 43,
    "date": "2025-06-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "C - Henry Dorkin",
    "lookup_name": "Henry Dorkin",
    "date": "2025-06-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 44,
    "lookup_name": "Henry Dorkin"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 44,
    "date": "2025-06-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "C - Bradley Reynolds",
    "lookup_name": "Bradley Reynolds",
    "date": "2025-06-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 45,
    "lookup_name": "Bradley Reynolds"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 45,
    "date": "2025-06-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "C - Nathan Sutcliffe ",
    "lookup_name": "Nathan Sutcliffe",
    "date": "2025-06-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient found
**Data:**
```json
{
    "patient_id": 46,
    "lookup_name": "Nathan Sutcliffe"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Surgery record found and marked as recorded
**Data:**
```json
{
    "patient_id": 46,
    "date": "2025-06-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-06-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 8,
    "recorded_count": 8,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "C - Michael Ridden",
    "lookup_name": "Michael Ridden",
    "date": "2025-07-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Michael Ridden"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "C- Daniel Wright ",
    "lookup_name": "Daniel Wright",
    "date": "2025-07-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Daniel Wright"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "R - Adam Quinn",
    "lookup_name": "R - Adam Quinn",
    "date": "2025-07-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R - Adam Quinn"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "C - Jake Cronshaw",
    "lookup_name": "Jake Cronshaw",
    "date": "2025-07-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Jake Cronshaw"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 12,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-12"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "C - Kye Best ",
    "lookup_name": "Kye Best",
    "date": "2025-07-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Kye Best"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "R - Didier Merveilleux",
    "lookup_name": "R - Didier Merveilleux",
    "date": "2025-07-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R - Didier Merveilleux"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "C - Daniel Haywood",
    "lookup_name": "Daniel Haywood",
    "date": "2025-07-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Daniel Haywood"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "C - Lee Cavanagh",
    "lookup_name": "Lee Cavanagh",
    "date": "2025-07-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Lee Cavanagh"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-07-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "C - Luke Hayward",
    "lookup_name": "Luke Hayward",
    "date": "2025-08-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Luke Hayward"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "C - JACK EDWARDS",
    "lookup_name": "JACK EDWARDS",
    "date": "2025-08-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "JACK EDWARDS"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "C - Michael Birch ",
    "lookup_name": "Michael Birch",
    "date": "2025-08-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Michael Birch"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "C- Alexander Gillies ",
    "lookup_name": "Alexander Gillies",
    "date": "2025-08-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Alexander Gillies"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "C - Shaun Abbott",
    "lookup_name": "Shaun Abbott",
    "date": "2025-08-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Shaun Abbott"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 16,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 17,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-17"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "C - James Gibson",
    "lookup_name": "James Gibson",
    "date": "2025-08-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "James Gibson"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "R-Daniel Foley",
    "lookup_name": "R-Daniel Foley",
    "date": "2025-08-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R-Daniel Foley"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 23,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 24,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-08-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "C - Aiden Pellett",
    "lookup_name": "Aiden Pellett",
    "date": "2025-09-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Aiden Pellett"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "C-Ross Beattie",
    "lookup_name": "Ross Beattie",
    "date": "2025-09-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Ross Beattie"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "R - Martin Parker",
    "lookup_name": "R - Martin Parker",
    "date": "2025-09-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R - Martin Parker"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "R - Jason Murby",
    "lookup_name": "R - Jason Murby",
    "date": "2025-09-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R - Jason Murby"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "C - Anthony Edmunds ",
    "lookup_name": "Anthony Edmunds",
    "date": "2025-09-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Anthony Edmunds"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-09-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "R-Paul Moran",
    "lookup_name": "R-Paul Moran",
    "date": "2025-09-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R-Paul Moran"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "C - Adam Saidi",
    "lookup_name": "Adam Saidi",
    "date": "2025-10-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Adam Saidi"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "C - Paulius Konisevas",
    "lookup_name": "Paulius Konisevas",
    "date": "2025-10-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Paulius Konisevas"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 5,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-05"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "C - JC Etienne",
    "lookup_name": "JC Etienne",
    "date": "2025-10-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "JC Etienne"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 12,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-12"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "C - Daniel Zanelli",
    "lookup_name": "Daniel Zanelli",
    "date": "2025-10-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Daniel Zanelli"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "C - Mike Shingleston ",
    "lookup_name": "Mike Shingleston",
    "date": "2025-10-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Mike Shingleston"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 24,
    "original_name": "C - David Dearle",
    "lookup_name": "David Dearle",
    "date": "2025-10-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "David Dearle"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-10-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "C - Tim Lester ",
    "lookup_name": "Tim Lester",
    "date": "2025-11-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Tim Lester"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "R - Oliver Green",
    "lookup_name": "R - Oliver Green",
    "date": "2025-11-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "R - Oliver Green"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 8,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-08"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 9,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-09"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "C - Michael Reichardt",
    "lookup_name": "Michael Reichardt",
    "date": "2025-11-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Michael Reichardt"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 15,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-15"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 16,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-16"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 22,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-22"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 23,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-23"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 29,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-11-29"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 6,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-06"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 13,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-13"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 14,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-14"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "C- Darren Hearn ",
    "lookup_name": "Darren Hearn",
    "date": "2025-12-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Darren Hearn"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "C - Dave Hustwick",
    "lookup_name": "Dave Hustwick",
    "date": "2025-12-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Dave Hustwick"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 20,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-20"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 21,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-21"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 26,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-26"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 27,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-27"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 28,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-12-28"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 1,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-01"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 2,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-02"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 3,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-03"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 4,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-04"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 7,
    "original_name": "C - Jacob Cordell",
    "lookup_name": "Jacob Cordell",
    "date": "2025-01-07"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Jacob Cordell"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 10,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-10"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 11,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-11"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 17,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-17"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 18,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-18"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 19,
    "original_name": "C- David Jamal Ali ",
    "lookup_name": "David Jamal Ali",
    "date": "2025-01-19"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "David Jamal Ali"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 24,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-24"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Processing patient lookup
**Data:**
```json
{
    "row": 25,
    "original_name": "Closed",
    "lookup_name": "Closed",
    "date": "2025-01-25"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient not found
**Data:**
```json
{
    "lookup_name": "Closed"
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 11:11:44

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 33.1,
    "final_memory_usage_mb": 0.5,
    "peak_memory_usage_mb": 0.5,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 11:18:13",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:D30"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.76,
    "memory_used_mb": 0.14,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 10,
    "recorded_count": 10,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 15,
    "recorded_count": 15,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 8,
    "recorded_count": 8,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 11:18:13

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.57,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 11:36:44",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:D30"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.69,
    "memory_used_mb": 0.14,
    "peak_memory_mb": 0.49
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 10,
    "recorded_count": 10,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 15,
    "recorded_count": 15,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 8,
    "recorded_count": 8,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 11:36:44

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.35,
    "final_memory_usage_mb": 0.5,
    "peak_memory_usage_mb": 0.5,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 11:54:09",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:D30"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.25,
    "memory_used_mb": 0.14,
    "peak_memory_mb": 0.49
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 10,
    "recorded_count": 10,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 15,
    "recorded_count": 15,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 8,
    "recorded_count": 8,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 11:54:09

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 10.29,
    "final_memory_usage_mb": 0.5,
    "peak_memory_usage_mb": 0.5,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 13:56:53",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 13:56:53

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 13:56:55

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "test",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 13:56:55

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 13:56:55

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:D30",
    "final_range": "'MAR 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:55

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 293.46
}
```
---

## Debug Log - 2025-05-27 13:56:55

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:D30",
    "final_range": "'APR 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 306.56
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:D30",
    "final_range": "'MAY 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 306.87
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:D30",
    "final_range": "'JUN 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 307.67
}
```
---

## Debug Log - 2025-05-27 13:56:56

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:D30",
    "final_range": "'JUL 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 303.79
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:D30",
    "final_range": "'AUG 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 310.68
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:D30",
    "final_range": "'SEP 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 238.4
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:D30",
    "final_range": "'OCT 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 230.19
}
```
---

## Debug Log - 2025-05-27 13:56:57

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:D30",
    "final_range": "'NOV 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 419.59
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:D30",
    "final_range": "'DEC 25'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 228.98
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:D30",
    "final_range": "'JAN 26'!A1:D30"
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 321.16
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 5248.73
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_e1dc2188bbeda43808601e8be1983cfd.json",
    "cache_size_bytes": 10962,
    "cache_size_kb": 10.71
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 5270.41,
    "memory_used_mb": 0.89,
    "peak_memory_mb": 1.28
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 10,
    "recorded_count": 10,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 15,
    "recorded_count": 15,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 8,
    "recorded_count": 8,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 13:56:58

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 5277.24,
    "final_memory_usage_mb": 1.27,
    "peak_memory_usage_mb": 1.28,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 14:36:24",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 14:36:24

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 14:36:26

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "test",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:36:26

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 14:36:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:Z",
    "final_range": "'MAR 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 32,
    "fetch_time_ms": 925.03
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:Z",
    "final_range": "'APR 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 31,
    "fetch_time_ms": 274.72
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:Z",
    "final_range": "'MAY 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 32,
    "fetch_time_ms": 630.73
}
```
---

## Debug Log - 2025-05-27 14:36:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:Z",
    "final_range": "'JUN 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 31,
    "fetch_time_ms": 539.23
}
```
---

## Debug Log - 2025-05-27 14:36:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:Z",
    "final_range": "'JUL 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 32,
    "fetch_time_ms": 233.75
}
```
---

## Debug Log - 2025-05-27 14:36:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:Z",
    "final_range": "'AUG 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:29

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 32,
    "fetch_time_ms": 458.18
}
```
---

## Debug Log - 2025-05-27 14:36:29

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:Z",
    "final_range": "'SEP 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:29

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 31,
    "fetch_time_ms": 245.68
}
```
---

## Debug Log - 2025-05-27 14:36:29

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:Z",
    "final_range": "'OCT 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 32,
    "fetch_time_ms": 1021.11
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:Z",
    "final_range": "'NOV 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 31,
    "fetch_time_ms": 256.81
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:Z",
    "final_range": "'DEC 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 32,
    "fetch_time_ms": 208.89
}
```
---

## Debug Log - 2025-05-27 14:36:30

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:Z",
    "final_range": "'JAN 26'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 32,
    "fetch_time_ms": 248.92
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 348,
    "total_api_time_ms": 6638.21
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_e1dc2188bbeda43808601e8be1983cfd.json",
    "cache_size_bytes": 21247,
    "cache_size_kb": 20.75
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 6644.7,
    "memory_used_mb": 0.3,
    "peak_memory_mb": 0.65
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 14:36:31

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 6656.34,
    "final_memory_usage_mb": 0.65,
    "peak_memory_usage_mb": 0.65,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 14:51:49",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 14:51:49

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 14:51:51

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "test",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:51:51

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 14:51:51

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:Z",
    "final_range": "'MAR 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:52

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 32,
    "fetch_time_ms": 885.41
}
```
---

## Debug Log - 2025-05-27 14:51:52

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:Z",
    "final_range": "'APR 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:53

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 31,
    "fetch_time_ms": 812.26
}
```
---

## Debug Log - 2025-05-27 14:51:53

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:Z",
    "final_range": "'MAY 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:54

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 32,
    "fetch_time_ms": 957.23
}
```
---

## Debug Log - 2025-05-27 14:51:54

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:Z",
    "final_range": "'JUN 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:55

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 31,
    "fetch_time_ms": 886.78
}
```
---

## Debug Log - 2025-05-27 14:51:55

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:Z",
    "final_range": "'JUL 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:56

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 32,
    "fetch_time_ms": 951.72
}
```
---

## Debug Log - 2025-05-27 14:51:56

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:Z",
    "final_range": "'AUG 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:57

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 32,
    "fetch_time_ms": 922.41
}
```
---

## Debug Log - 2025-05-27 14:51:57

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:Z",
    "final_range": "'SEP 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:58

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 31,
    "fetch_time_ms": 876.65
}
```
---

## Debug Log - 2025-05-27 14:51:58

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:Z",
    "final_range": "'OCT 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:58

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 32,
    "fetch_time_ms": 823.68
}
```
---

## Debug Log - 2025-05-27 14:51:58

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:Z",
    "final_range": "'NOV 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:51:59

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 31,
    "fetch_time_ms": 853.18
}
```
---

## Debug Log - 2025-05-27 14:51:59

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:Z",
    "final_range": "'DEC 25'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:52:00

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 32,
    "fetch_time_ms": 889.75
}
```
---

## Debug Log - 2025-05-27 14:52:00

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:Z",
    "final_range": "'JAN 26'!A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 32,
    "fetch_time_ms": 869.42
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 348,
    "total_api_time_ms": 11764.95
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_e1dc2188bbeda43808601e8be1983cfd.json",
    "cache_size_bytes": 21247,
    "cache_size_kb": 20.75
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 11769.05,
    "memory_used_mb": 0.3,
    "peak_memory_mb": 0.65
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 14:52:01

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 11774.13,
    "final_memory_usage_mb": 0.65,
    "peak_memory_usage_mb": 0.65,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 14:52:04",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 1.99,
    "memory_used_mb": 0.19,
    "peak_memory_mb": 0.54
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 14:52:04

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 6.77,
    "final_memory_usage_mb": 0.54,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 14:54:57",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:Z"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.28,
    "memory_used_mb": 0.19,
    "peak_memory_mb": 0.54
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 2,
    "recorded_count": 2,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 14:54:57

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.42,
    "final_memory_usage_mb": 0.54,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:04:28",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.8,
    "memory_used_mb": 0.19,
    "peak_memory_mb": 0.54
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 15:04:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:04:29

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 9.27,
    "final_memory_usage_mb": 0.54,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:04:44",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.14,
    "memory_used_mb": 0.19,
    "peak_memory_mb": 0.54
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:04:44

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 7.8,
    "final_memory_usage_mb": 0.54,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:04:48",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.53,
    "memory_used_mb": 0.19,
    "peak_memory_mb": 0.54
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 17,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 31,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 18,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 9
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 24,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 30
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 31
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:04:48

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 10.88,
    "final_memory_usage_mb": 0.54,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:04:58",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 15:04:58

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 15:05:00

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "test",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:00

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 15:05:00

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:01

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 1020.38
}
```
---

## Debug Log - 2025-05-27 15:05:01

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:02

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 881.86
}
```
---

## Debug Log - 2025-05-27 15:05:02

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:03

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 948.64
}
```
---

## Debug Log - 2025-05-27 15:05:03

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:04

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 865.54
}
```
---

## Debug Log - 2025-05-27 15:05:04

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:05

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 975.63
}
```
---

## Debug Log - 2025-05-27 15:05:05

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:06

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 915.34
}
```
---

## Debug Log - 2025-05-27 15:05:06

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:07

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 857.23
}
```
---

## Debug Log - 2025-05-27 15:05:07

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:08

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 903.63
}
```
---

## Debug Log - 2025-05-27 15:05:08

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:09

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 997.38
}
```
---

## Debug Log - 2025-05-27 15:05:09

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:10

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 925.95
}
```
---

## Debug Log - 2025-05-27 15:05:10

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 917.43
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 12324.16
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_e1dc2188bbeda43808601e8be1983cfd.json",
    "cache_size_bytes": 17040,
    "cache_size_kb": 16.64
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 12330.67,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.63
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:05:11

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 12341.18,
    "final_memory_usage_mb": 0.63,
    "peak_memory_usage_mb": 0.63,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:05:16",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.39,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:05:16

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 6.93,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 15:05:24",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.96,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 3,
    "recorded_count": 3,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 15:05:24

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.89,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 16:10:48",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 16:10:48

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 16:10:50

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "test",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:10:50

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 16:10:50

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:50

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 802.32
}
```
---

## Debug Log - 2025-05-27 16:10:50

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:51

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 478.69
}
```
---

## Debug Log - 2025-05-27 16:10:51

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:51

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 349.75
}
```
---

## Debug Log - 2025-05-27 16:10:51

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 300.94
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 623.57
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 301.79
}
```
---

## Debug Log - 2025-05-27 16:10:52

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:53

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 958.59
}
```
---

## Debug Log - 2025-05-27 16:10:53

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 271.37
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 299.09
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 315.64
}
```
---

## Debug Log - 2025-05-27 16:10:54

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 293.56
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 6549.93
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_e1dc2188bbeda43808601e8be1983cfd.json",
    "cache_size_bytes": 17040,
    "cache_size_kb": 16.64
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 6554.28,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.63
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 2,
    "recorded_count": 2,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 16:10:55

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 6560.99,
    "final_memory_usage_mb": 0.63,
    "peak_memory_usage_mb": 0.63,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 16:11:05",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.36,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 2,
    "recorded_count": 2,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 16:11:05

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 7.16,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 16:45:30",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 2.62,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 2,
    "recorded_count": 2,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 16:45:30

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 7.16,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 16:47:11",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/sanctuary.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 1.82,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.52
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 17,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 16:47:11

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 6.34,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:38:12",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:38:12

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1dGY4Ir9TpnGxwfBUnNrytBPDtJl93BWSbU95V2jfWZY"
}
```
---

## Debug Log - 2025-05-27 23:38:13

**Level:** ERROR
**Message:** ERROR: API_002 - Google Service Exception
**Data:**
```json
{
    "code": 403,
    "message": "{\n  \"error\": {\n    \"code\": 403,\n    \"message\": \"The caller does not have permission\",\n    \"errors\": [\n      {\n        \"message\": \"The caller does not have permission\",\n        \"domain\": \"global\",\n        \"reason\": \"forbidden\"\n      }\n    ],\n    \"status\": \"PERMISSION_DENIED\"\n  }\n}\n",
    "errors": [
        {
            "message": "The caller does not have permission",
            "domain": "global",
            "reason": "forbidden"
        }
    ]
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:41:18",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:41:18

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-27 23:41:19

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:41:19

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 23:41:19

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:20

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 813.62
}
```
---

## Debug Log - 2025-05-27 23:41:20

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:21

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 766.26
}
```
---

## Debug Log - 2025-05-27 23:41:21

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:22

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 860.02
}
```
---

## Debug Log - 2025-05-27 23:41:22

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:22

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 710.34
}
```
---

## Debug Log - 2025-05-27 23:41:22

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:23

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 820.09
}
```
---

## Debug Log - 2025-05-27 23:41:23

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:24

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 721.23
}
```
---

## Debug Log - 2025-05-27 23:41:24

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 814.26
}
```
---

## Debug Log - 2025-05-27 23:41:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 715.84
}
```
---

## Debug Log - 2025-05-27 23:41:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:26

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 816.79
}
```
---

## Debug Log - 2025-05-27 23:41:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 818.71
}
```
---

## Debug Log - 2025-05-27 23:41:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 794.61
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 9780.19
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 17115,
    "cache_size_kb": 16.71
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 9783.87,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.63
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:41:28

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 9793.55,
    "final_memory_usage_mb": 0.63,
    "peak_memory_usage_mb": 0.63,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:42:25",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:42:25

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-27 23:42:26

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:42:26

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 23:42:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:K30",
    "final_range": "'MAR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 1042.33
}
```
---

## Debug Log - 2025-05-27 23:42:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:K30",
    "final_range": "'APR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 717.88
}
```
---

## Debug Log - 2025-05-27 23:42:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:K30",
    "final_range": "'MAY 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 701.74
}
```
---

## Debug Log - 2025-05-27 23:42:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:K30",
    "final_range": "'JUN 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 226.61
}
```
---

## Debug Log - 2025-05-27 23:42:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:K30",
    "final_range": "'JUL 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:29

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 785.41
}
```
---

## Debug Log - 2025-05-27 23:42:29

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:K30",
    "final_range": "'AUG 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:30

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 745.7
}
```
---

## Debug Log - 2025-05-27 23:42:30

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:K30",
    "final_range": "'SEP 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:31

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 787.49
}
```
---

## Debug Log - 2025-05-27 23:42:31

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:K30",
    "final_range": "'OCT 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:31

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 757.75
}
```
---

## Debug Log - 2025-05-27 23:42:31

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:K30",
    "final_range": "'NOV 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:32

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 773.25
}
```
---

## Debug Log - 2025-05-27 23:42:32

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:K30",
    "final_range": "'DEC 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:33

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 738.09
}
```
---

## Debug Log - 2025-05-27 23:42:33

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:K30",
    "final_range": "'JAN 26'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 819.91
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 8551.42
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 20544,
    "cache_size_kb": 20.06
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 8555.84,
    "memory_used_mb": 0.29,
    "peak_memory_mb": 0.64
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:42:34

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8562.58,
    "final_memory_usage_mb": 0.64,
    "peak_memory_usage_mb": 0.65,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:43:04",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:43:04

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-27 23:43:05

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:43:05

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 23:43:05

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:K30",
    "final_range": "'MAR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:06

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 783.56
}
```
---

## Debug Log - 2025-05-27 23:43:06

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:K30",
    "final_range": "'APR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:06

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 226.21
}
```
---

## Debug Log - 2025-05-27 23:43:06

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:K30",
    "final_range": "'MAY 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:07

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 746.41
}
```
---

## Debug Log - 2025-05-27 23:43:07

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:K30",
    "final_range": "'JUN 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:07

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 826.45
}
```
---

## Debug Log - 2025-05-27 23:43:07

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:K30",
    "final_range": "'JUL 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:08

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 714.22
}
```
---

## Debug Log - 2025-05-27 23:43:08

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:K30",
    "final_range": "'AUG 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:09

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 819.09
}
```
---

## Debug Log - 2025-05-27 23:43:09

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:K30",
    "final_range": "'SEP 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:10

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 818.77
}
```
---

## Debug Log - 2025-05-27 23:43:10

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:K30",
    "final_range": "'OCT 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:11

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 814.09
}
```
---

## Debug Log - 2025-05-27 23:43:11

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:K30",
    "final_range": "'NOV 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:11

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 217.95
}
```
---

## Debug Log - 2025-05-27 23:43:11

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:K30",
    "final_range": "'DEC 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 699.74
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:K30",
    "final_range": "'JAN 26'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 743.04
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 8344.65
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 20544,
    "cache_size_kb": 20.06
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 8352.2,
    "memory_used_mb": 0.29,
    "peak_memory_mb": 0.64
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 21,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:43:12

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8364.18,
    "final_memory_usage_mb": 0.64,
    "peak_memory_usage_mb": 0.65,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:44:01",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:44:01

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-27 23:44:02

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:44:02

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 23:44:02

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:K30",
    "final_range": "'MAR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:03

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 808.98
}
```
---

## Debug Log - 2025-05-27 23:44:03

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:K30",
    "final_range": "'APR 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:04

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 684.83
}
```
---

## Debug Log - 2025-05-27 23:44:04

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:K30",
    "final_range": "'MAY 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:05

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 808.08
}
```
---

## Debug Log - 2025-05-27 23:44:05

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:K30",
    "final_range": "'JUN 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:05

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 222.95
}
```
---

## Debug Log - 2025-05-27 23:44:05

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:K30",
    "final_range": "'JUL 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:05

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 712.04
}
```
---

## Debug Log - 2025-05-27 23:44:06

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:K30",
    "final_range": "'AUG 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:06

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 737.5
}
```
---

## Debug Log - 2025-05-27 23:44:06

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:K30",
    "final_range": "'SEP 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:07

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 488.02
}
```
---

## Debug Log - 2025-05-27 23:44:07

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:K30",
    "final_range": "'OCT 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:07

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 684.34
}
```
---

## Debug Log - 2025-05-27 23:44:07

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:K30",
    "final_range": "'NOV 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:08

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 736.48
}
```
---

## Debug Log - 2025-05-27 23:44:08

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:K30",
    "final_range": "'DEC 25'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:09

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 816.6
}
```
---

## Debug Log - 2025-05-27 23:44:09

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:K30",
    "final_range": "'JAN 26'!A1:K30"
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 681.69
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 8605.2
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 20538,
    "cache_size_kb": 20.06
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 8609.74,
    "memory_used_mb": 0.29,
    "peak_memory_mb": 0.64
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:44:10

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8620.08,
    "final_memory_usage_mb": 0.64,
    "peak_memory_usage_mb": 0.65,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:52:01",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.12,
    "memory_used_mb": 0.18,
    "peak_memory_mb": 0.53
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:52:01

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 10.16,
    "final_memory_usage_mb": 0.53,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:53:17",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 3000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 4
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.35,
    "memory_used_mb": 0.18,
    "peak_memory_mb": 0.53
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:53:17

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 7.66,
    "final_memory_usage_mb": 0.53,
    "peak_memory_usage_mb": 0.54,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:53:28",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 2
}
```
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-27 23:53:28

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-27 23:53:29

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:29

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-27 23:53:29

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:30

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 814.76
}
```
---

## Debug Log - 2025-05-27 23:53:30

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:31

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 713.57
}
```
---

## Debug Log - 2025-05-27 23:53:31

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:31

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 819.76
}
```
---

## Debug Log - 2025-05-27 23:53:31

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:32

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 751.79
}
```
---

## Debug Log - 2025-05-27 23:53:32

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:33

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 746.14
}
```
---

## Debug Log - 2025-05-27 23:53:33

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:34

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 752.22
}
```
---

## Debug Log - 2025-05-27 23:53:34

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:35

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 822.21
}
```
---

## Debug Log - 2025-05-27 23:53:35

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:35

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 739.05
}
```
---

## Debug Log - 2025-05-27 23:53:35

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:36

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 810.4
}
```
---

## Debug Log - 2025-05-27 23:53:36

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:37

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 797.86
}
```
---

## Debug Log - 2025-05-27 23:53:37

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 819.7
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 9618.32
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 16986,
    "cache_size_kb": 16.59
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 9623.04,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.62
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:53:38

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 9630.19,
    "final_memory_usage_mb": 0.63,
    "peak_memory_usage_mb": 0.63,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-27 23:53:44",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 5
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 10000,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 5
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 3.51,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.51
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 4,
    "recorded_count": 4,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-27 23:53:44

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.22,
    "final_memory_usage_mb": 0.52,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 2,
    "timestamp": "2025-05-28 00:42:19",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/133.0.0.0 Safari\/537.36 OPR\/118.0.0.0"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 2
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-28 00:42:19

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-28 00:42:20

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 00:42:20

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-28 00:42:20

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:20

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 746.03
}
```
---

## Debug Log - 2025-05-28 00:42:20

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:21

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 787.63
}
```
---

## Debug Log - 2025-05-28 00:42:21

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:22

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 821.03
}
```
---

## Debug Log - 2025-05-28 00:42:22

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:23

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 712.51
}
```
---

## Debug Log - 2025-05-28 00:42:23

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:24

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 791.64
}
```
---

## Debug Log - 2025-05-28 00:42:24

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:24

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 212.73
}
```
---

## Debug Log - 2025-05-28 00:42:24

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 970.34
}
```
---

## Debug Log - 2025-05-28 00:42:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 311.72
}
```
---

## Debug Log - 2025-05-28 00:42:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:26

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 714.32
}
```
---

## Debug Log - 2025-05-28 00:42:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:26

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 702.34
}
```
---

## Debug Log - 2025-05-28 00:42:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 752.54
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 8670.29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 16986,
    "cache_size_kb": 16.59
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 8673.9,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.62
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-28 00:42:27

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8681.65,
    "final_memory_usage_mb": 0.62,
    "peak_memory_usage_mb": 0.62,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-28 00:42:45",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": true,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 1.92,
    "memory_used_mb": 0.16,
    "peak_memory_mb": 0.51
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 19,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 10
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 23,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 8
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-28 00:42:45

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 8.48,
    "final_memory_usage_mb": 0.51,
    "peak_memory_usage_mb": 0.52,
    "cache_used": true,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-28 15:25:19",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "spreadsheet_id"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "spreadsheet_id",
    "found": "YES",
    "value_length": 44
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cell_range"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cell_range",
    "found": "YES",
    "value_length": 6
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Configuration loaded
**Data:**
```json
{
    "credentials_path": ".\/secrets\/liv-hsh-patients-18682cec86db.json",
    "spreadsheet_id": "SET",
    "cache_duration": 300,
    "cell_range": "A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Retrieving setting from database
**Data:**
```json
{
    "key": "cache_duration"
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Setting retrieved
**Data:**
```json
{
    "key": "cache_duration",
    "found": "YES",
    "value_length": 3
}
```
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Initializing Google Client
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Google Client initialized successfully
---

## Debug Log - 2025-05-28 15:25:19

**Level:** INFO
**Message:** Fetching spreadsheet metadata
**Data:**
```json
{
    "spreadsheet_id": "1mP20et8Pe_RMQvEC-ra2mXi9aoAtTwK_jsMwcUn9tt0"
}
```
---

## Debug Log - 2025-05-28 15:25:21

**Level:** INFO
**Message:** Spreadsheet metadata retrieved
**Data:**
```json
{
    "title": "Liv Harley Street HT Bookings",
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 15:25:21

**Level:** INFO
**Message:** Starting data processing for each sheet
---

## Debug Log - 2025-05-28 15:25:21

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 0,
    "sheet_title": "MAR 25",
    "escaped_title": "MAR 25",
    "cell_range": "A1:I30",
    "final_range": "'MAR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:21

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "row_count": 30,
    "fetch_time_ms": 805.1
}
```
---

## Debug Log - 2025-05-28 15:25:21

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 1,
    "sheet_title": "APR 25",
    "escaped_title": "APR 25",
    "cell_range": "A1:I30",
    "final_range": "'APR 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:22

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "APR 25",
    "row_count": 30,
    "fetch_time_ms": 858.84
}
```
---

## Debug Log - 2025-05-28 15:25:22

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 2,
    "sheet_title": "MAY 25",
    "escaped_title": "MAY 25",
    "cell_range": "A1:I30",
    "final_range": "'MAY 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:23

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "row_count": 30,
    "fetch_time_ms": 703.91
}
```
---

## Debug Log - 2025-05-28 15:25:23

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 3,
    "sheet_title": "JUN 25",
    "escaped_title": "JUN 25",
    "cell_range": "A1:I30",
    "final_range": "'JUN 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:24

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "row_count": 30,
    "fetch_time_ms": 915.11
}
```
---

## Debug Log - 2025-05-28 15:25:24

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 4,
    "sheet_title": "JUL 25",
    "escaped_title": "JUL 25",
    "cell_range": "A1:I30",
    "final_range": "'JUL 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "row_count": 30,
    "fetch_time_ms": 924.91
}
```
---

## Debug Log - 2025-05-28 15:25:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 5,
    "sheet_title": "AUG 25",
    "escaped_title": "AUG 25",
    "cell_range": "A1:I30",
    "final_range": "'AUG 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:25

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "row_count": 30,
    "fetch_time_ms": 722.64
}
```
---

## Debug Log - 2025-05-28 15:25:25

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 6,
    "sheet_title": "SEP 25",
    "escaped_title": "SEP 25",
    "cell_range": "A1:I30",
    "final_range": "'SEP 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:26

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "row_count": 30,
    "fetch_time_ms": 764.16
}
```
---

## Debug Log - 2025-05-28 15:25:26

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 7,
    "sheet_title": "OCT 25",
    "escaped_title": "OCT 25",
    "cell_range": "A1:I30",
    "final_range": "'OCT 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:27

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "row_count": 30,
    "fetch_time_ms": 792.37
}
```
---

## Debug Log - 2025-05-28 15:25:27

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 8,
    "sheet_title": "NOV 25",
    "escaped_title": "NOV 25",
    "cell_range": "A1:I30",
    "final_range": "'NOV 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "row_count": 30,
    "fetch_time_ms": 784.85
}
```
---

## Debug Log - 2025-05-28 15:25:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 9,
    "sheet_title": "DEC 25",
    "escaped_title": "DEC 25",
    "cell_range": "A1:I30",
    "final_range": "'DEC 25'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:28

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "row_count": 30,
    "fetch_time_ms": 312.16
}
```
---

## Debug Log - 2025-05-28 15:25:28

**Level:** INFO
**Message:** Fetching data for sheet
**Data:**
```json
{
    "sheet_index": 10,
    "sheet_title": "JAN 26",
    "escaped_title": "JAN 26",
    "cell_range": "A1:I30",
    "final_range": "'JAN 26'!A1:I30"
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Sheet data retrieved successfully
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "row_count": 30,
    "fetch_time_ms": 873.09
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** All sheet data retrieved
**Data:**
```json
{
    "total_sheets": 11,
    "total_rows": 330,
    "total_api_time_ms": 9521.86
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Saving data to cache
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Cache saved
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 17025,
    "cache_size_kb": 16.63
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting display rendering
**Data:**
```json
{
    "using_cache": false,
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Performance metrics
**Data:**
```json
{
    "total_execution_time_ms": 9526.45,
    "memory_used_mb": 0.27,
    "peak_memory_mb": 0.62
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Rendering sheet tabs and content
**Data:**
```json
{
    "sheet_count": 11
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAR 25",
    "patient_lookups": 15,
    "surgery_lookups": 1,
    "recorded_count": 1,
    "buttons_created": 4
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "APR 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "MAY 25",
    "patient_lookups": 29,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 15
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUN 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 11
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JUL 25",
    "patient_lookups": 20,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "AUG 25",
    "patient_lookups": 22,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "SEP 25",
    "patient_lookups": 15,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 7
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "OCT 25",
    "patient_lookups": 14,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 6
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "NOV 25",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "DEC 25",
    "patient_lookups": 13,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 3
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Starting patient/surgery lookup for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "data_rows": 29
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Patient/surgery lookup completed for sheet
**Data:**
```json
{
    "sheet_title": "JAN 26",
    "patient_lookups": 12,
    "surgery_lookups": 0,
    "recorded_count": 0,
    "buttons_created": 2
}
```
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Display rendering completed successfully
---

## Debug Log - 2025-05-28 15:25:29

**Level:** INFO
**Message:** Workflow completed successfully
**Data:**
```json
{
    "total_execution_time_ms": 9533.37,
    "final_memory_usage_mb": 0.63,
    "peak_memory_usage_mb": 0.63,
    "cache_used": false,
    "sheets_processed": 11
}
```
---

## Debug Log - 2025-05-29 14:56:02

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 14:56:02",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 14:56:02

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 14:56:02

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 1.02,
    "final_memory_usage_mb": 0.37,
    "peak_memory_usage_mb": 0.44
}
```
---

## Debug Log - 2025-05-29 14:56:03

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 14:56:07

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 4448.27
}
```
---

## Debug Log - 2025-05-29 14:56:07

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 14:56:10

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 14:56:10",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 14:56:10

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 14:56:10

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 1.08,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.36
}
```
---

## Debug Log - 2025-05-29 14:56:10

**Level:** INFO
**Message:** Using cached data in API
---

## Debug Log - 2025-05-29 14:56:17

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 14:56:17",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 14:56:17

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 14:56:17

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.51,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.36
}
```
---

## Debug Log - 2025-05-29 14:56:17

**Level:** INFO
**Message:** Using cached data in API
---

## Debug Log - 2025-05-29 14:56:26

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 14:56:26",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 14:56:26

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 14:56:26

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.73,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.36
}
```
---

## Debug Log - 2025-05-29 14:56:26

**Level:** INFO
**Message:** Using cached data in API
---

## Debug Log - 2025-05-29 14:56:54

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 14:56:54",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 14:56:54

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 14:56:54

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.56,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.36
}
```
---

## Debug Log - 2025-05-29 14:56:55

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 14:56:59

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 4186.06
}
```
---

## Debug Log - 2025-05-29 14:56:59

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 15:00:21

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 15:00:21",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 15:00:21

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 15:00:21

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 1.43,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.43
}
```
---

## Debug Log - 2025-05-29 15:00:21

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 15:00:24

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 3257.47
}
```
---

## Debug Log - 2025-05-29 15:00:24

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 15:00:56

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 15:00:56",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 15:00:56

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 15:00:56

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.85,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.43
}
```
---

## Debug Log - 2025-05-29 15:00:56

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 15:00:59

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 3339.11
}
```
---

## Debug Log - 2025-05-29 15:00:59

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 15:01:27

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 15:01:27",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 15:01:27

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 15:01:27

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.9,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.43
}
```
---

## Debug Log - 2025-05-29 15:01:27

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 15:01:30

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 3388.68
}
```
---

## Debug Log - 2025-05-29 15:01:30

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 15:01:44

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 15:01:44",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 15:01:44

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 15:01:44

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 1.11,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.43
}
```
---

## Debug Log - 2025-05-29 15:01:44

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 15:01:48

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 3707.86
}
```
---

## Debug Log - 2025-05-29 15:01:48

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 16:09:45

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 16:09:45",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 16:09:45

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 16:09:45

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 1,
    "final_memory_usage_mb": 0.37,
    "peak_memory_usage_mb": 0.44
}
```
---

## Debug Log - 2025-05-29 16:09:45

**Level:** INFO
**Message:** Cache not available, fetching from Google Sheets API in API endpoint
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** All sheet data retrieved in API endpoint
**Data:**
```json
{
    "total_sheets": 12,
    "total_api_time_ms": 8072.06
}
```
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** Cache saved in API
**Data:**
```json
{
    "cache_file": "sheet_data_d3150955807ecdc9b83bed9d480c6b56.json",
    "cache_size_bytes": 18220
}
```
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** Starting Google Sheets workflow
**Data:**
```json
{
    "user_id": 1,
    "timestamp": "2025-05-29 16:09:54",
    "user_agent": "Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"
}
```
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** Authentication successful
**Data:**
```json
{
    "user_id": 1
}
```
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** Initial page load completed
**Data:**
```json
{
    "total_execution_time_ms": 0.48,
    "final_memory_usage_mb": 0.36,
    "peak_memory_usage_mb": 0.36
}
```
---

## Debug Log - 2025-05-29 16:09:54

**Level:** INFO
**Message:** Using cached data in API
---
